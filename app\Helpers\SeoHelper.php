<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Request;

class SeoHelper
{
    /**
     * Cache duration in minutes (24 hours)
     */
    const CACHE_DURATION = 60 * 24;

    /**
     * Get all active languages with caching
     */
    public static function getActiveLanguages()
    {
        return Cache::remember('seo_active_languages', self::CACHE_DURATION, function () {
            return collect(config('languages'))
                ->filter(fn($lang) => $lang['status'] == 1)
                ->values();
        });
    }

    /**
     * Generate hreflang tags for SEO
     */
    public static function generateHreflangTags($currentPath = null)
    {
        $currentPath = $currentPath ?? request()->path();
        $languages = self::getActiveLanguages();
        $hreflangs = [];

        foreach ($languages as $lang) {
            $code = $lang['code'];
            
            // Remove existing language prefix from path
            $cleanPath = self::removeLanguagePrefix($currentPath);
            
            // Build URL with language prefix
            $url = url('/' . $code . '/' . ltrim($cleanPath, '/'));
            
            $hreflangs[] = [
                'hreflang' => $code,
                'href' => $url
            ];
        }

        return $hreflangs;
    }

    /**
     * Generate canonical URL for current page
     */
    public static function getCanonicalUrl($currentPath = null)
    {
        $currentPath = $currentPath ?? request()->path();
        $currentLocale = App::getLocale();
        
        // Remove existing language prefix and rebuild with current locale
        $cleanPath = self::removeLanguagePrefix($currentPath);
        
        return url('/' . $currentLocale . '/' . ltrim($cleanPath, '/'));
    }

    /**
     * Remove language prefix from path
     */
    public static function removeLanguagePrefix($path)
    {
        $languages = self::getActiveLanguages();
        $codes = $languages->pluck('code')->all();
        
        $segments = explode('/', trim($path, '/'));
        
        if (!empty($segments[0]) && in_array($segments[0], $codes)) {
            array_shift($segments);
        }
        
        return implode('/', $segments);
    }

    /**
     * Generate language-specific URL
     */
    public static function getLanguageUrl($locale, $currentPath = null)
    {
        $currentPath = $currentPath ?? request()->path();
        
        // Remove existing language prefix
        $cleanPath = self::removeLanguagePrefix($currentPath);
        
        // Build new URL with specified locale
        $newPath = '/' . $locale . '/' . ltrim($cleanPath, '/');
        
        // Preserve query parameters
        $queryString = request()->getQueryString();
        if ($queryString) {
            $newPath .= '?' . $queryString;
        }
        
        return url($newPath);
    }

    /**
     * Get language direction (RTL/LTR)
     */
    public static function getLanguageDirection($locale = null)
    {
        $locale = $locale ?? App::getLocale();
        
        // RTL languages
        $rtlLanguages = ['ar', 'he', 'fa', 'ur', 'ku', 'ps'];
        
        return in_array($locale, $rtlLanguages) ? 'rtl' : 'ltr';
    }

    /**
     * Generate complete SEO meta data
     */
    public static function getSeoMetaData($options = [])
    {
        $currentLocale = App::getLocale();
        $currentPath = request()->path();
        
        return [
            'lang' => $currentLocale,
            'dir' => self::getLanguageDirection($currentLocale),
            'canonical' => self::getCanonicalUrl($currentPath),
            'hreflang' => self::generateHreflangTags($currentPath),
            'og_url' => self::getCanonicalUrl($currentPath),
            'alternate_languages' => self::getAlternateLanguages($currentPath)
        ];
    }

    /**
     * Get alternate language URLs
     */
    public static function getAlternateLanguages($currentPath = null)
    {
        $currentPath = $currentPath ?? request()->path();
        $currentLocale = App::getLocale();
        $languages = self::getActiveLanguages();
        $alternates = [];

        foreach ($languages as $lang) {
            if ($lang['code'] !== $currentLocale) {
                $alternates[] = [
                    'code' => $lang['code'],
                    'name' => $lang['name'],
                    'url' => self::getLanguageUrl($lang['code'], $currentPath)
                ];
            }
        }

        return $alternates;
    }

    /**
     * Generate structured data for language alternatives
     */
    public static function getStructuredLanguageData()
    {
        $currentPath = request()->path();
        $languages = self::getActiveLanguages();
        $structuredData = [];

        foreach ($languages as $lang) {
            $structuredData[] = [
                '@type' => 'WebPage',
                '@id' => self::getLanguageUrl($lang['code'], $currentPath),
                'inLanguage' => $lang['code'],
                'name' => $lang['name']
            ];
        }

        return $structuredData;
    }

    /**
     * Check if current URL has proper language prefix
     */
    public static function hasLanguagePrefix($path = null)
    {
        $path = $path ?? request()->path();
        $languages = self::getActiveLanguages();
        $codes = $languages->pluck('code')->all();
        
        $firstSegment = explode('/', trim($path, '/'))[0] ?? '';
        
        return in_array($firstSegment, $codes);
    }

    /**
     * Get default language code
     */
    public static function getDefaultLanguage()
    {
        return config('app.locale', 'en');
    }

    /**
     * Clear language-related cache
     */
    public static function clearCache()
    {
        Cache::forget('seo_active_languages');
        Cache::forget('active_languages_config');
        
        // Clear individual language caches
        $languages = config('languages', []);
        foreach ($languages as $lang) {
            Cache::forget("language_code_{$lang['code']}");
            Cache::forget("current_language_{$lang['code']}");
            Cache::forget("other_languages_{$lang['code']}");
        }
    }
}
