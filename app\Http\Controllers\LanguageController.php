<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use App\Helpers\SeoHelper;

class LanguageController extends Controller
{
    /**
     * Set the application language and redirect to proper URL
     */
    public function setLanguage(Request $request, $lang)
    {
        // Get active languages with caching
        $activeLanguages = Cache::remember('active_languages_codes', 3600, function () {
            return collect(config('languages'))
                ->filter(fn($language) => $language['status'] == 1)
                ->pluck('code')
                ->all();
        });

        // Validate language code
        if (!in_array($lang, $activeLanguages)) {
            $lang = config('app.locale', 'en');
        }

        // Get current path and build new URL
        $currentPath = $request->path();
        $redirectUrl = SeoHelper::getLanguageUrl($lang, $currentPath);

        // Set cookie with optimized settings for performance and security
        $cookieMinutes = 60 * 24 * 365 * 2; // 2 years (reduced from 5 for better performance)

        return redirect($redirectUrl, 302)->withCookie(
            cookie(
                'locale',
                $lang,
                $cookieMinutes,
                '/',
                null,
                request()->secure(), // Use HTTPS if available
                true, // HTTP only
                false, // Raw cookie
                'Lax' // SameSite policy
            )
        );
    }

    /**
     * Get current language information for AJAX requests
     */
    public function getCurrentLanguage()
    {
        $currentLocale = app()->getLocale();
        $languages = Cache::remember('active_languages_config', 3600, function () {
            return collect(config('languages'))
                ->filter(fn($lang) => $lang['status'] == 1)
                ->values();
        });

        $current = $languages->firstWhere('code', $currentLocale);
        $others = $languages->where('code', '!=', $currentLocale)->values();

        return response()->json([
            'current' => $current,
            'others' => $others,
            'available_codes' => $languages->pluck('code')->all()
        ]);
    }

    /**
     * Clear language cache (for admin use)
     */
    public function clearCache()
    {
        Cache::forget('active_languages_codes');
        Cache::forget('active_languages_config');
        SeoHelper::clearCache();

        return response()->json(['message' => 'Language cache cleared successfully']);
    }
}