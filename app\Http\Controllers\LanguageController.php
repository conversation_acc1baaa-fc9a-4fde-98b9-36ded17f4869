<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class LanguageController extends Controller
{

     public function setLanguage(Request $request, $lang)
    {
        $allowed = collect(config('languages'))->pluck('code')->all();

        if (!in_array($lang, $allowed)) {
            $lang = config('app.locale');
        }

        $minutes = 60 * 24 * 365 * 5; // 5 years
        $segments = $request->segments();

        // Replace first segment if it's a locale
        if (isset($segments[0]) && in_array($segments[0], $allowed)) {
            $segments[0] = $lang;
        } else {
            array_unshift($segments, $lang);
        }

        $redirectUrl = url(implode('/', $segments));

        return redirect($redirectUrl)->withCookie(
            cookie('locale', $lang, $minutes, '/', null, true, true, false, 'Lax')
        );
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}