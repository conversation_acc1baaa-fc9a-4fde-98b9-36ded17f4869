<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;

class UrlHelper
{
    /**
     * Generate localized route URL
     */
    public static function route($name, $parameters = [], $locale = null)
    {
        $locale = $locale ?? App::getLocale();
        
        // Add locale to parameters
        $parameters = array_merge(['locale' => $locale], $parameters);
        
        return route($name, $parameters);
    }

    /**
     * Generate URL for specific language
     */
    public static function localizedUrl($path = '', $locale = null, $parameters = [])
    {
        $locale = $locale ?? App::getLocale();
        
        // Clean the path
        $path = ltrim($path, '/');
        
        // Build URL with language prefix
        $url = url('/' . $locale . '/' . $path);
        
        // Add query parameters if provided
        if (!empty($parameters)) {
            $url .= '?' . http_build_query($parameters);
        }
        
        return $url;
    }

    /**
     * Generate current URL in different language
     */
    public static function currentUrlInLanguage($locale)
    {
        return SeoHelper::getLanguageUrl($locale);
    }

    /**
     * Get home URL for specific language
     */
    public static function homeUrl($locale = null)
    {
        $locale = $locale ?? App::getLocale();
        return url('/' . $locale);
    }

    /**
     * Generate language switcher URLs
     */
    public static function getLanguageSwitcherUrls()
    {
        $currentPath = request()->path();
        $languages = SeoHelper::getActiveLanguages();
        $urls = [];

        foreach ($languages as $lang) {
            $urls[$lang['code']] = [
                'name' => $lang['name'],
                'code' => $lang['code'],
                'url' => SeoHelper::getLanguageUrl($lang['code'], $currentPath),
                'is_current' => $lang['code'] === App::getLocale()
            ];
        }

        return $urls;
    }

    /**
     * Generate breadcrumb URLs with language support
     */
    public static function breadcrumbUrl($routeName, $parameters = [], $locale = null)
    {
        return self::route($routeName, $parameters, $locale);
    }

    /**
     * Generate asset URL with language context (for language-specific assets)
     */
    public static function assetUrl($path, $locale = null)
    {
        $locale = $locale ?? App::getLocale();
        
        // Check if language-specific asset exists
        $langSpecificPath = "assets/{$locale}/{$path}";
        $defaultPath = "assets/{$path}";
        
        // Return language-specific asset if it exists, otherwise default
        if (file_exists(public_path($langSpecificPath))) {
            return asset($langSpecificPath);
        }
        
        return asset($defaultPath);
    }

    /**
     * Generate canonical URL for current page
     */
    public static function canonical()
    {
        return SeoHelper::getCanonicalUrl();
    }

    /**
     * Check if URL belongs to current language
     */
    public static function isCurrentLanguageUrl($url)
    {
        $currentLocale = App::getLocale();
        $path = parse_url($url, PHP_URL_PATH);
        $segments = explode('/', trim($path, '/'));
        
        return isset($segments[0]) && $segments[0] === $currentLocale;
    }

    /**
     * Generate sitemap URLs for all languages
     */
    public static function getSitemapUrls($routes = [])
    {
        $languages = SeoHelper::getActiveLanguages();
        $sitemapUrls = [];

        foreach ($languages as $lang) {
            $locale = $lang['code'];
            
            foreach ($routes as $route) {
                $sitemapUrls[] = [
                    'url' => self::route($route['name'], $route['params'] ?? [], $locale),
                    'locale' => $locale,
                    'priority' => $route['priority'] ?? 0.8,
                    'changefreq' => $route['changefreq'] ?? 'weekly'
                ];
            }
        }

        return $sitemapUrls;
    }

    /**
     * Generate language-aware pagination URLs
     */
    public static function paginationUrl($page, $routeName, $parameters = [], $locale = null)
    {
        $parameters['page'] = $page;
        return self::route($routeName, $parameters, $locale);
    }

    /**
     * Generate search URL with language context
     */
    public static function searchUrl($query = '', $locale = null)
    {
        $locale = $locale ?? App::getLocale();
        $parameters = [];
        
        if (!empty($query)) {
            $parameters['q'] = $query;
        }
        
        return self::localizedUrl('search', $locale, $parameters);
    }

    /**
     * Generate contact URL for specific language
     */
    public static function contactUrl($locale = null)
    {
        return self::route('contact', [], $locale);
    }

    /**
     * Generate about URL for specific language
     */
    public static function aboutUrl($locale = null)
    {
        return self::route('about', [], $locale);
    }

    /**
     * Generate services URL for specific language
     */
    public static function servicesUrl($locale = null)
    {
        return self::route('services', [], $locale);
    }

    /**
     * Generate blog URL for specific language
     */
    public static function blogUrl($locale = null)
    {
        return self::route('blog', [], $locale);
    }

    /**
     * Generate blog detail URL for specific language
     */
    public static function blogDetailUrl($slug, $locale = null)
    {
        return self::route('blog.detail', ['slug' => $slug], $locale);
    }

    /**
     * Get all available language codes
     */
    public static function getAvailableLanguages()
    {
        return Cache::remember('url_helper_languages', 3600, function () {
            return collect(config('languages'))
                ->filter(fn($lang) => $lang['status'] == 1)
                ->pluck('code')
                ->all();
        });
    }

    /**
     * Check if given locale is valid
     */
    public static function isValidLocale($locale)
    {
        return in_array($locale, self::getAvailableLanguages());
    }

    /**
     * Get default locale
     */
    public static function getDefaultLocale()
    {
        return config('app.locale', 'en');
    }

    /**
     * Generate URL with proper trailing slash handling
     */
    public static function normalizeUrl($url)
    {
        // Remove trailing slash except for root URLs
        $url = rtrim($url, '/');
        
        // Add trailing slash for root language URLs (e.g., /en, /ar)
        $path = parse_url($url, PHP_URL_PATH);
        $segments = explode('/', trim($path, '/'));
        
        if (count($segments) === 1 && self::isValidLocale($segments[0])) {
            $url .= '/';
        }
        
        return $url;
    }
}
