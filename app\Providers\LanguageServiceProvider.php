<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Blade;
use App\Helpers\SeoHelper;
use App\Helpers\UrlHelper;

class LanguageServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register()
    {
        // Register language-related singletons for better performance
        $this->app->singleton('language.config', function () {
            return Cache::remember('language_config_optimized', 86400, function () {
                return collect(config('languages'))
                    ->filter(fn($lang) => $lang['status'] == 1)
                    ->keyBy('code')
                    ->map(fn($lang) => (object) $lang);
            });
        });

        $this->app->singleton('language.codes', function () {
            return Cache::remember('language_codes_optimized', 86400, function () {
                return collect(config('languages'))
                    ->filter(fn($lang) => $lang['status'] == 1)
                    ->pluck('code')
                    ->all();
            });
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot()
    {
        // Register Blade directives for language handling
        $this->registerBladeDirectives();
        
        // Share language data with all views for performance
        $this->shareLanguageDataWithViews();
        
        // Register view composers for common language data
        $this->registerViewComposers();
    }

    /**
     * Register custom Blade directives
     */
    protected function registerBladeDirectives()
    {
        // @lang directive for language-specific URLs
        Blade::directive('lang', function ($expression) {
            return "<?php echo App\Helpers\UrlHelper::route($expression); ?>";
        });

        // @langUrl directive for simple language URLs
        Blade::directive('langUrl', function ($expression) {
            return "<?php echo App\Helpers\UrlHelper::localizedUrl($expression); ?>";
        });

        // @canonical directive for canonical URLs
        Blade::directive('canonical', function () {
            return "<?php echo App\Helpers\SeoHelper::getCanonicalUrl(); ?>";
        });

        // @hreflang directive for hreflang tags
        Blade::directive('hreflang', function () {
            return "<?php 
                \$hreflangs = App\Helpers\SeoHelper::generateHreflangTags();
                foreach (\$hreflangs as \$hreflang) {
                    echo '<link rel=\"alternate\" hreflang=\"' . \$hreflang['hreflang'] . '\" href=\"' . \$hreflang['href'] . '\" />' . PHP_EOL;
                }
            ?>";
        });

        // @currentLang directive
        Blade::directive('currentLang', function () {
            return "<?php echo app()->getLocale(); ?>";
        });

        // @isRtl directive
        Blade::directive('isRtl', function () {
            return "<?php echo App\Helpers\SeoHelper::getLanguageDirection() === 'rtl' ? 'true' : 'false'; ?>";
        });
    }

    /**
     * Share optimized language data with all views
     */
    protected function shareLanguageDataWithViews()
    {
        View::composer('*', function ($view) {
            // Only compute language data once per request
            if (!app()->bound('shared.language.data')) {
                $currentLocale = app()->getLocale();
                $languages = app('language.config');
                
                $languageData = [
                    'currentLocale' => $currentLocale,
                    'currentLang' => $languages->get($currentLocale),
                    'availableLanguages' => $languages,
                    'otherLanguages' => $languages->except($currentLocale),
                    'languageCodes' => app('language.codes'),
                    'isRtl' => SeoHelper::getLanguageDirection($currentLocale) === 'rtl',
                    'languageDirection' => SeoHelper::getLanguageDirection($currentLocale)
                ];
                
                app()->instance('shared.language.data', $languageData);
            }
            
            $view->with(app('shared.language.data'));
        });
    }

    /**
     * Register view composers for specific components
     */
    protected function registerViewComposers()
    {
        // Language switcher composer
        View::composer(['website.include.header', 'website.include.language-switcher'], function ($view) {
            $languageSwitcherData = Cache::remember(
                'language_switcher_' . app()->getLocale() . '_' . request()->path(),
                3600,
                function () {
                    return UrlHelper::getLanguageSwitcherUrls();
                }
            );
            
            $view->with('languageSwitcherUrls', $languageSwitcherData);
        });

        // SEO meta composer
        View::composer(['website.include.layout', 'website.include.head'], function ($view) {
            $seoData = Cache::remember(
                'seo_meta_' . app()->getLocale() . '_' . request()->path(),
                3600,
                function () {
                    return SeoHelper::getSeoMetaData();
                }
            );
            
            $view->with('seoData', $seoData);
        });

        // Navigation composer with language-aware URLs
        View::composer(['website.include.navigation', 'website.include.menu'], function ($view) {
            $navigationUrls = Cache::remember(
                'navigation_urls_' . app()->getLocale(),
                3600,
                function () {
                    $locale = app()->getLocale();
                    return [
                        'home' => UrlHelper::homeUrl($locale),
                        'about' => UrlHelper::aboutUrl($locale),
                        'services' => UrlHelper::servicesUrl($locale),
                        'contact' => UrlHelper::contactUrl($locale),
                        'blog' => UrlHelper::blogUrl($locale),
                    ];
                }
            );
            
            $view->with('navUrls', $navigationUrls);
        });
    }

    /**
     * Clear all language-related caches
     */
    public static function clearLanguageCache()
    {
        $cacheKeys = [
            'language_config_optimized',
            'language_codes_optimized',
            'seo_active_languages',
            'active_languages_config',
            'active_languages_codes',
        ];

        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }

        // Clear pattern-based caches
        $patterns = [
            'language_switcher_*',
            'seo_meta_*',
            'navigation_urls_*',
            'current_language_*',
            'other_languages_*',
            'language_code_*'
        ];

        // Note: This would require a cache store that supports pattern deletion
        // For now, we'll clear specific known keys
        $locales = config('languages', []);
        foreach ($locales as $lang) {
            if (isset($lang['code'])) {
                Cache::forget("current_language_{$lang['code']}");
                Cache::forget("other_languages_{$lang['code']}");
                Cache::forget("language_code_{$lang['code']}");
                Cache::forget("navigation_urls_{$lang['code']}");
            }
        }

        // Clear SeoHelper cache
        SeoHelper::clearCache();
    }

    /**
     * Warm up language caches
     */
    public static function warmUpCache()
    {
        // Pre-load language configuration
        app('language.config');
        app('language.codes');

        // Pre-load SEO data for each language
        $locales = app('language.codes');
        foreach ($locales as $locale) {
            app()->setLocale($locale);
            SeoHelper::getSeoMetaData();
            UrlHelper::getLanguageSwitcherUrls();
        }

        // Reset to default locale
        app()->setLocale(config('app.locale', 'en'));
    }
}
