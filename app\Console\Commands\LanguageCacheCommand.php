<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Providers\LanguageServiceProvider;

class LanguageCacheCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'language:cache {action=warm : The action to perform (warm, clear, refresh)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Manage language-related caches for optimal performance';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'warm':
                $this->warmUpCache();
                break;
            case 'clear':
                $this->clearCache();
                break;
            case 'refresh':
                $this->refreshCache();
                break;
            default:
                $this->error("Invalid action: {$action}. Use 'warm', 'clear', or 'refresh'.");
                return 1;
        }

        return 0;
    }

    /**
     * Warm up language caches
     */
    protected function warmUpCache()
    {
        $this->info('Warming up language caches...');
        
        try {
            LanguageServiceProvider::warmUpCache();
            $this->info('✓ Language caches warmed up successfully!');
        } catch (\Exception $e) {
            $this->error('Failed to warm up caches: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Clear language caches
     */
    protected function clearCache()
    {
        $this->info('Clearing language caches...');
        
        try {
            LanguageServiceProvider::clearLanguageCache();
            $this->info('✓ Language caches cleared successfully!');
        } catch (\Exception $e) {
            $this->error('Failed to clear caches: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Refresh language caches (clear then warm up)
     */
    protected function refreshCache()
    {
        $this->info('Refreshing language caches...');
        
        try {
            $this->clearCache();
            $this->warmUpCache();
            $this->info('✓ Language caches refreshed successfully!');
        } catch (\Exception $e) {
            $this->error('Failed to refresh caches: ' . $e->getMessage());
            return 1;
        }
    }
}
