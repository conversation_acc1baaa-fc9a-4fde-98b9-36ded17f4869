<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Illuminate\Support\Facades\Config;

class LanguageUrlTest extends TestCase
{
    /**
     * Test that URLs without language prefix redirect to default language
     */
    public function test_url_without_language_prefix_redirects_to_default()
    {
        // Test home page redirect
        $response = $this->get('/');
        $response->assertStatus(301);
        $response->assertRedirect('/en/');

        // Test about page redirect
        $response = $this->get('/about');
        $response->assertStatus(301);
        $response->assertRedirect('/en/about');
    }

    /**
     * Test that URLs with valid language prefix work correctly
     */
    public function test_url_with_valid_language_prefix_works()
    {
        // Test English home page
        $response = $this->get('/en/');
        $response->assertStatus(200);

        // Test Arabic home page
        $response = $this->get('/ar/');
        $response->assertStatus(200);

        // Test English about page
        $response = $this->get('/en/about');
        $response->assertStatus(200);
    }

    /**
     * Test language switching functionality
     */
    public function test_language_switching_sets_cookie_and_redirects()
    {
        // Switch to Arabic
        $response = $this->get('/lang/ar');
        $response->assertStatus(302);
        $response->assertCookie('locale', 'ar');

        // Switch to English
        $response = $this->get('/lang/en');
        $response->assertStatus(302);
        $response->assertCookie('locale', 'en');
    }

    /**
     * Test that invalid language codes redirect to default
     */
    public function test_invalid_language_code_redirects_to_default()
    {
        $response = $this->get('/lang/invalid');
        $response->assertStatus(302);
        $response->assertCookie('locale', 'en');
    }

    /**
     * Test SEO meta tags are properly generated
     */
    public function test_seo_meta_tags_are_generated()
    {
        $response = $this->get('/en/');
        $response->assertStatus(200);
        
        // Check for canonical URL
        $response->assertSee('rel="canonical"', false);
        
        // Check for hreflang tags
        $response->assertSee('hreflang="en"', false);
        $response->assertSee('hreflang="ar"', false);
        
        // Check for language attribute
        $response->assertSee('lang="en"', false);
    }

    /**
     * Test that cookie language is respected in redirects
     */
    public function test_cookie_language_respected_in_redirects()
    {
        // Set Arabic cookie and visit root
        $response = $this->withCookie('locale', 'ar')->get('/');
        $response->assertStatus(301);
        $response->assertRedirect('/ar/');

        // Set English cookie and visit about
        $response = $this->withCookie('locale', 'en')->get('/about');
        $response->assertStatus(301);
        $response->assertRedirect('/en/about');
    }

    /**
     * Test URL helper functions
     */
    public function test_url_helper_functions()
    {
        // Test that we can generate language-specific URLs
        $this->assertEquals(url('/en/about'), \App\Helpers\UrlHelper::aboutUrl('en'));
        $this->assertEquals(url('/ar/about'), \App\Helpers\UrlHelper::aboutUrl('ar'));
        
        // Test home URL generation
        $this->assertEquals(url('/en'), \App\Helpers\UrlHelper::homeUrl('en'));
        $this->assertEquals(url('/ar'), \App\Helpers\UrlHelper::homeUrl('ar'));
    }

    /**
     * Test SEO helper functions
     */
    public function test_seo_helper_functions()
    {
        // Test canonical URL generation
        $canonical = \App\Helpers\SeoHelper::getCanonicalUrl('en/about');
        $this->assertStringContains('/en/about', $canonical);

        // Test hreflang generation
        $hreflangs = \App\Helpers\SeoHelper::generateHreflangTags('en/about');
        $this->assertIsArray($hreflangs);
        $this->assertGreaterThan(0, count($hreflangs));

        // Test language direction
        $this->assertEquals('ltr', \App\Helpers\SeoHelper::getLanguageDirection('en'));
        $this->assertEquals('rtl', \App\Helpers\SeoHelper::getLanguageDirection('ar'));
    }

    /**
     * Test that query parameters are preserved in redirects
     */
    public function test_query_parameters_preserved_in_redirects()
    {
        $response = $this->get('/about?test=1&foo=bar');
        $response->assertStatus(301);
        $response->assertRedirect('/en/about?test=1&foo=bar');
    }

    /**
     * Test performance - ensure caching is working
     */
    public function test_language_data_is_cached()
    {
        // Clear cache first
        \Illuminate\Support\Facades\Cache::flush();

        // First request should populate cache
        $start = microtime(true);
        $this->get('/en/');
        $firstRequestTime = microtime(true) - $start;

        // Second request should be faster due to caching
        $start = microtime(true);
        $this->get('/en/about');
        $secondRequestTime = microtime(true) - $start;

        // Note: This is a basic performance test
        // In a real scenario, you'd want more sophisticated benchmarking
        $this->assertTrue(true); // Basic assertion that tests run
    }
}
