<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\View;
use App\Http\Controllers\website\website;
use App\Http\Controllers\LanguageController;


Route::group(
    [
        'prefix' => '{locale?}',
        'where' => ['locale' => implode('|', collect(config('languages'))->pluck('code')->all())],
        'middleware' => ['lang']
    ],
    function () {
        Route::get('lang/{locale}', [LanguageController::class, 'setLanguage'])->name('setlocale');

        Route::get('/', [website::class, 'HomeView'])->name('home');
        Route::get('/about', [website::class, 'AboutView'])->name('about');
        Route::get('/contact', fn() => view('website.contact'))->name('contact');
        Route::get('/services', fn() => view('website.service'))->name('services');
        Route::get('/internal-and-external-shipping', fn() => view('website.internalAndExternalShipping'))->name('shipping');
        Route::get('/storage-and-warehouse-management', fn() => view('website.storageAndWarehouseManagement'))->name('warehouse');
        Route::get('/business-specific-solutions', fn() => view('website.businessSpecificSolutions'))->name('solutions');

        Route::get('/blog', [website::class, 'blogView'])->name('blog');
        Route::get('/blog/{slug}', [website::class, 'singleBlogView'])->name('blog.detail');

        Route::get('/404', fn() => view('website.404'))->name('404');
    }
);
