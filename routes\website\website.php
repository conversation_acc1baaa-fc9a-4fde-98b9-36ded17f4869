<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\website\website;
use App\Http\Controllers\LanguageController;

// Get active language codes for route constraints
$activeLangCodes = collect(config('languages'))
    ->filter(fn($lang) => $lang['status'] == 1)
    ->pluck('code')
    ->implode('|');

// Language switching route (outside the main group to avoid conflicts)
Route::get('lang/{locale}', [LanguageController::class, 'setLanguage'])
    ->name('setlocale')
    ->where('locale', $activeLangCodes)
    ->middleware('web');

// Main website routes with required language prefix
Route::group([
    'prefix' => '{locale}',
    'where' => ['locale' => $activeLangCodes],
    'middleware' => ['web', 'lang']
], function () {
    // Website routes
    Route::get('/', [website::class, 'HomeView'])->name('home');
    Route::get('/about', [website::class, 'AboutView'])->name('about');
    Route::get('/contact', fn() => view('website.contact'))->name('contact');
    Route::get('/services', fn() => view('website.service'))->name('services');
    Route::get('/internal-and-external-shipping', fn() => view('website.internalAndExternalShipping'))->name('shipping');
    Route::get('/storage-and-warehouse-management', fn() => view('website.storageAndWarehouseManagement'))->name('warehouse');
    Route::get('/business-specific-solutions', fn() => view('website.businessSpecificSolutions'))->name('solutions');

    // Blog routes
    Route::get('/blog', [website::class, 'blogView'])->name('blog');
    Route::get('/blog/{slug}', [website::class, 'singleBlogView'])->name('blog.detail');

    // 404 page
    Route::get('/404', fn() => view('website.404'))->name('404');
});
