<?php

/**
 * Language Implementation Validation Script
 * 
 * This script validates the multilingual implementation
 * Run this from the project root: php validate-language-implementation.php
 */

require_once 'vendor/autoload.php';

class LanguageImplementationValidator
{
    private $errors = [];
    private $warnings = [];
    private $success = [];

    public function validate()
    {
        echo "🔍 Validating Language Implementation...\n\n";

        $this->checkConfigFiles();
        $this->checkHelperFiles();
        $this->checkMiddleware();
        $this->checkRoutes();
        $this->checkViews();
        $this->checkServiceProvider();

        $this->displayResults();
    }

    private function checkConfigFiles()
    {
        echo "📁 Checking Configuration Files...\n";

        // Check languages.php config
        if (file_exists('config/languages.php')) {
            $this->success[] = "✓ config/languages.php exists";
            
            $languages = include 'config/languages.php';
            if (is_array($languages) && count($languages) > 0) {
                $this->success[] = "✓ Languages configuration is valid";
                
                $hasEnglish = false;
                $hasArabic = false;
                foreach ($languages as $lang) {
                    if ($lang['code'] === 'en') $hasEnglish = true;
                    if ($lang['code'] === 'ar') $hasArabic = true;
                }
                
                if ($hasEnglish) {
                    $this->success[] = "✓ English language configured";
                } else {
                    $this->errors[] = "✗ English language not found in configuration";
                }
                
                if ($hasArabic) {
                    $this->success[] = "✓ Arabic language configured";
                } else {
                    $this->warnings[] = "⚠ Arabic language not found in configuration";
                }
            } else {
                $this->errors[] = "✗ Languages configuration is invalid";
            }
        } else {
            $this->errors[] = "✗ config/languages.php not found";
        }

        // Check app.php for default locale
        if (file_exists('config/app.php')) {
            $content = file_get_contents('config/app.php');
            if (strpos($content, "'locale' => env('APP_LOCALE', 'en')") !== false) {
                $this->success[] = "✓ Default locale set to English";
            } else {
                $this->warnings[] = "⚠ Default locale might not be set to English";
            }
        }

        echo "\n";
    }

    private function checkHelperFiles()
    {
        echo "🔧 Checking Helper Files...\n";

        $helpers = [
            'app/Helpers/SeoHelper.php' => 'SEO Helper',
            'app/Helpers/UrlHelper.php' => 'URL Helper',
        ];

        foreach ($helpers as $file => $name) {
            if (file_exists($file)) {
                $this->success[] = "✓ {$name} exists";
                
                $content = file_get_contents($file);
                if (strpos($content, 'class ' . str_replace(' Helper', 'Helper', $name)) !== false) {
                    $this->success[] = "✓ {$name} class structure is valid";
                } else {
                    $this->errors[] = "✗ {$name} class structure is invalid";
                }
            } else {
                $this->errors[] = "✗ {$name} not found at {$file}";
            }
        }

        echo "\n";
    }

    private function checkMiddleware()
    {
        echo "⚙️ Checking Middleware...\n";

        if (file_exists('app/Http/Middleware/LanguageMiddleware.php')) {
            $this->success[] = "✓ LanguageMiddleware exists";
            
            $content = file_get_contents('app/Http/Middleware/LanguageMiddleware.php');
            
            if (strpos($content, 'redirect(') !== false) {
                $this->success[] = "✓ LanguageMiddleware has redirect logic";
            } else {
                $this->errors[] = "✗ LanguageMiddleware missing redirect logic";
            }
            
            if (strpos($content, 'Cache::remember') !== false) {
                $this->success[] = "✓ LanguageMiddleware uses caching";
            } else {
                $this->warnings[] = "⚠ LanguageMiddleware might not use caching";
            }
        } else {
            $this->errors[] = "✗ LanguageMiddleware not found";
        }

        // Check if middleware is registered
        if (file_exists('app/Http/Kernel.php')) {
            $content = file_get_contents('app/Http/Kernel.php');
            if (strpos($content, 'LanguageMiddleware') !== false) {
                $this->success[] = "✓ LanguageMiddleware is registered";
            } else {
                $this->errors[] = "✗ LanguageMiddleware not registered in Kernel";
            }
        }

        echo "\n";
    }

    private function checkRoutes()
    {
        echo "🛣️ Checking Routes...\n";

        if (file_exists('routes/website/website.php')) {
            $this->success[] = "✓ Website routes file exists";
            
            $content = file_get_contents('routes/website/website.php');
            
            if (strpos($content, "prefix' => '{locale}'") !== false) {
                $this->success[] = "✓ Routes have language prefix configuration";
            } else {
                $this->errors[] = "✗ Routes missing language prefix configuration";
            }
            
            if (strpos($content, 'LanguageController') !== false) {
                $this->success[] = "✓ Language switching route exists";
            } else {
                $this->errors[] = "✗ Language switching route not found";
            }
        } else {
            $this->errors[] = "✗ Website routes file not found";
        }

        echo "\n";
    }

    private function checkViews()
    {
        echo "👁️ Checking Views...\n";

        $views = [
            'resources/views/website/include/layout.blade.php' => 'Main layout',
            'resources/views/website/include/header.blade.php' => 'Header template',
        ];

        foreach ($views as $file => $name) {
            if (file_exists($file)) {
                $this->success[] = "✓ {$name} exists";
                
                $content = file_get_contents($file);
                
                if (strpos($content, 'SeoHelper') !== false || strpos($content, 'UrlHelper') !== false) {
                    $this->success[] = "✓ {$name} uses helper classes";
                } else {
                    $this->warnings[] = "⚠ {$name} might not use helper classes";
                }
                
                if (strpos($content, 'hreflang') !== false) {
                    $this->success[] = "✓ {$name} includes hreflang tags";
                } else {
                    $this->warnings[] = "⚠ {$name} might not include hreflang tags";
                }
            } else {
                $this->warnings[] = "⚠ {$name} not found at {$file}";
            }
        }

        echo "\n";
    }

    private function checkServiceProvider()
    {
        echo "🔌 Checking Service Provider...\n";

        if (file_exists('app/Providers/LanguageServiceProvider.php')) {
            $this->success[] = "✓ LanguageServiceProvider exists";
            
            $content = file_get_contents('app/Providers/LanguageServiceProvider.php');
            
            if (strpos($content, 'Blade::directive') !== false) {
                $this->success[] = "✓ Custom Blade directives registered";
            } else {
                $this->warnings[] = "⚠ Custom Blade directives might not be registered";
            }
        } else {
            $this->warnings[] = "⚠ LanguageServiceProvider not found";
        }

        // Check if service provider is registered
        if (file_exists('bootstrap/providers.php')) {
            $content = file_get_contents('bootstrap/providers.php');
            if (strpos($content, 'LanguageServiceProvider') !== false) {
                $this->success[] = "✓ LanguageServiceProvider is registered";
            } else {
                $this->warnings[] = "⚠ LanguageServiceProvider might not be registered";
            }
        }

        echo "\n";
    }

    private function displayResults()
    {
        echo "📊 Validation Results:\n";
        echo "==================\n\n";

        if (!empty($this->success)) {
            echo "✅ SUCCESS (" . count($this->success) . " items):\n";
            foreach ($this->success as $item) {
                echo "   {$item}\n";
            }
            echo "\n";
        }

        if (!empty($this->warnings)) {
            echo "⚠️ WARNINGS (" . count($this->warnings) . " items):\n";
            foreach ($this->warnings as $item) {
                echo "   {$item}\n";
            }
            echo "\n";
        }

        if (!empty($this->errors)) {
            echo "❌ ERRORS (" . count($this->errors) . " items):\n";
            foreach ($this->errors as $item) {
                echo "   {$item}\n";
            }
            echo "\n";
        }

        // Overall status
        if (empty($this->errors)) {
            if (empty($this->warnings)) {
                echo "🎉 PERFECT! Your language implementation is complete and optimized!\n";
            } else {
                echo "✅ GOOD! Your language implementation is working with minor warnings.\n";
            }
        } else {
            echo "❌ ISSUES FOUND! Please fix the errors above before proceeding.\n";
        }

        echo "\n📝 Next Steps:\n";
        echo "1. Run: php artisan language:cache warm\n";
        echo "2. Test your URLs: /en/, /ar/, /en/about, /ar/about\n";
        echo "3. Check language switching functionality\n";
        echo "4. Validate SEO meta tags in browser\n";
        echo "5. Test performance with caching enabled\n";
    }
}

// Run validation
$validator = new LanguageImplementationValidator();
$validator->validate();
