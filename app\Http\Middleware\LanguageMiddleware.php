<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Cache;

class LanguageMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        // Get active languages with caching for performance
        $languages = Cache::remember('active_languages_config', 3600, function () {
            return collect(config('languages'))
                ->filter(fn($lang) => $lang['status'] == 1)
                ->values();
        });

        $codes = $languages->pluck('code')->all();
        $defaultLocale = config('app.locale', 'en');

        // Get first URL segment
        $firstSegment = $request->segment(1);
        $locale = null;

        // Check if URL has language prefix
        if ($firstSegment && in_array($firstSegment, $codes)) {
            $locale = $firstSegment;
        } else {
            // No language prefix in URL - need to redirect
            $cookieLocale = $request->cookie('locale');

            // Use cookie language if valid, otherwise use default
            $redirectLocale = (in_array($cookieLocale, $codes)) ? $cookieLocale : $defaultLocale;

            // Build redirect URL with language prefix
            $path = $request->path();
            $redirectPath = '/' . $redirectLocale . '/' . ltrim($path, '/');

            // Preserve query parameters
            $queryString = $request->getQueryString();
            if ($queryString) {
                $redirectPath .= '?' . $queryString;
            }

            return redirect($redirectPath, 301);
        }

        // Set application locale
        App::setLocale($locale);

        // Share optimized language data with views
        $current = $languages->firstWhere('code', $locale);
        $others = $languages->where('code', '!=', $locale)->values();

        View::share('cLang', (object) $current);
        View::share('otherLangs', $others->map(fn($l) => (object)$l));
        View::share('currentLocale', $locale);
        View::share('availableLocales', $codes);

        return $next($request);
    }
}
